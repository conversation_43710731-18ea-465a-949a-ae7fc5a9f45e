package org.Ver_zhzh.util;

import org.Ver_zhzh.shoot.ParticleHelper;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Player;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 过渡版ParticleEffect类，内部使用ParticleHelper实现
 * 此类可以让现有代码不需要大量修改就能使用新的ParticleHelper功能
 */
public enum ParticleEffect {
    // 定义常用的粒子效果类型，使用1.21版本兼容的Bukkit粒子类型
    EXPLOSION_NORMAL(Particle.POOF),
    EXPLOSION_LARGE(Particle.EXPLOSION),
    EXPLOSION_HUGE(Particle.EXPLOSION_EMITTER),
    FIREWORKS_SPARK(Particle.FIREWORK),
    WATER_BUBBLE(Particle.BUBBLE),
    WATER_SPLASH(Particle.SPLASH),
    WATER_WAKE(Particle.FISHING),
    SUSPENDED(Particle.UNDERWATER),
    SUSPENDED_DEPTH(Particle.UNDERWATER),
    CRIT(Particle.CRIT),
    CRIT_MAGIC(Particle.ENCHANTED_HIT),
    SMOKE_NORMAL(Particle.SMOKE),
    SMOKE_LARGE(Particle.LARGE_SMOKE),
    SPELL(Particle.EFFECT),
    SPELL_INSTANT(Particle.INSTANT_EFFECT),
    SPELL_MOB(Particle.ENTITY_EFFECT),
    SPELL_MOB_AMBIENT(Particle.ENTITY_EFFECT),
    SPELL_WITCH(Particle.WITCH),
    DRIP_WATER(Particle.DRIPPING_WATER),
    DRIP_LAVA(Particle.DRIPPING_LAVA),
    VILLAGER_ANGRY(Particle.ANGRY_VILLAGER),
    VILLAGER_HAPPY(Particle.HAPPY_VILLAGER),
    TOWN_AURA(Particle.MYCELIUM),
    NOTE(Particle.NOTE),
    PORTAL(Particle.PORTAL),
    ENCHANTMENT_TABLE(Particle.ENCHANT),
    FLAME(Particle.FLAME),
    LAVA(Particle.LAVA),
    CLOUD(Particle.CLOUD),
    REDSTONE(Particle.DUST),
    SNOWBALL(Particle.ITEM_SNOWBALL),
    SNOW_SHOVEL(Particle.ITEM_SNOWBALL),
    SLIME(Particle.ITEM_SLIME),
    HEART(Particle.HEART),
    ITEM_CRACK(Particle.ITEM),
    BLOCK_CRACK(Particle.BLOCK),
    BLOCK_DUST(Particle.BLOCK);

    private static ParticleHelper particleHelper;
    private final Particle bukkitParticle;
    
    /**
     * 构造函数
     */
    ParticleEffect(Particle bukkitParticle) {
        this.bukkitParticle = bukkitParticle;
    }

    /**
     * 初始化ParticleHelper
     */
    public static void initParticleHelper(ParticleHelper helper) {
        particleHelper = helper;
        Bukkit.getLogger().info("§a[ParticleEffect] 成功初始化ParticleHelper");
    }

    /**
     * 向所有玩家发送粒子效果
     */
    public void send(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        if (particleHelper == null) {
            particleHelper = new ParticleHelper(Bukkit.getPluginManager().getPlugin("Shoot"));
            Bukkit.getLogger().info("§e[ParticleEffect] 自动初始化ParticleHelper");
        }
        particleHelper.displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, bukkitParticle);
    }

    /**
     * 向单个玩家发送粒子效果
     */
    public void send(Player player, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        if (particleHelper == null) {
            particleHelper = new ParticleHelper(Bukkit.getPluginManager().getPlugin("Shoot"));
            Bukkit.getLogger().info("§e[ParticleEffect] 自动初始化ParticleHelper");
        }
        particleHelper.displayParticle(player, location, offsetX, offsetY, offsetZ, speed, count, bukkitParticle);
    }

    /**
     * 获取对应的Bukkit原生粒子类型
     */
    public Particle getBukkitParticle() {
        return bukkitParticle;
    }

    /**
     * 安全地根据字符串获取ParticleEffect，如果不存在返回默认值
     */
    public static ParticleEffect safeValueOf(String name) {
        try {
            return ParticleEffect.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            Bukkit.getLogger().warning("§c[ParticleEffect] 未找到粒子类型: " + name + "，使用默认值CLOUD");
            return ParticleEffect.CLOUD; // 默认返回CLOUD
        }
    }

    /**
     * 清理资源
     */
    public static void dispose() {
        if (particleHelper != null) {
            particleHelper.dispose();
            particleHelper = null;
            Bukkit.getLogger().info("§c[ParticleEffect] 已释放资源");
        }
    }
}